# Voltage Drop Table Generator

This Python project generates a professionally formatted Excel file with voltage drop calculations for Torre Cariblu 34.

## Files

- `voltage_table.py` - Main script that generates the Excel file
- `requirements.txt` - Python dependencies
- `run_script.sh` - Automated setup and execution script
- `README.md` - This documentation file

## Dependencies

The script requires the following Python libraries:
- `pandas` (>=2.0.0) - Data manipulation and analysis
- `openpyxl` (>=3.1.0) - Excel file creation and formatting

## Setup and Usage

### Method 1: Automatic Setup (Recommended)
```bash
./run_script.sh
```

### Method 2: Manual Setup
1. Install dependencies:
   ```bash
   python3 -m pip install -r requirements.txt
   ```

2. Run the script:
   ```bash
   python3 voltage_table.py
   ```

### Method 3: Direct Installation
```bash
python3 -m pip install pandas openpyxl
python3 voltage_table.py
```

## Output

The script generates an Excel file named `CAIDA_VOLTAJE_TORRE_CARIBLU_34_Formatted.xlsx` with:
- Professional engineering blue color scheme
- Formula reference included
- Proper numeric formatting
- Auto-sized columns
- Clean borders and styling

## System Requirements

- Python 3.7 or higher
- macOS, Linux, or Windows
- Internet connection for initial dependency installation

## Troubleshooting

If you encounter issues:
1. Ensure Python 3 is installed: `python3 --version`
2. Check if pip is available: `python3 -m pip --version`
3. Try updating pip: `python3 -m pip install --upgrade pip`
