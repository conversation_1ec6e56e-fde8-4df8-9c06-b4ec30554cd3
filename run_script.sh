#!/bin/bash

# Python Project Setup and Execution Script
# This script sets up the environment and runs the voltage table script

echo "🔧 Setting up Python environment..."

# Check if Python 3 is available
if ! command -v python3 &> /dev/null; then
    echo "❌ Python 3 is not installed. Please install Python 3 first."
    exit 1
fi

echo "✅ Python 3 found: $(python3 --version)"

# Install dependencies if requirements.txt exists
if [ -f "requirements.txt" ]; then
    echo "📦 Installing dependencies from requirements.txt..."
    python3 -m pip install -r requirements.txt
else
    echo "📦 Installing required libraries directly..."
    python3 -m pip install pandas openpyxl
fi

echo "🚀 Running voltage_table.py..."
python3 voltage_table.py

echo "✨ Script execution completed!"
