import pandas as pd
from openpyxl import Workbook
from openpyxl.styles import F<PERSON>, PatternFill, Border, Side, Alignment
from openpyxl.utils import get_column_letter

# Create the voltage drop data
voltage_data = [
    # Table 1 - Circuits A, B, C
    ["P-A2 (2do PISO)", "THW No.6", 225.40, 26240, 12, 28.80, 5.94, 2.85],
    ["P-A3 (3er PISO)", "THW No.4", 235.40, 41740, 12, 28.80, 3.90, 1.87],
    ["P-A4 (4to PISO)", "THW No.4", 245.40, 41740, 12, 28.80, 4.06, 1.95],
    ["P-A5 (5to PISO)", "THW No.4", 255.40, 41740, 12, 28.80, 4.23, 2.03],
    ["P-A6 (6to PISO)", "THW No.4", 265.40, 41740, 12, 28.80, 4.39, 2.11],
    ["P-A7 (7mo PISO)", "THW No.4", 275.40, 41740, 12, 28.80, 4.56, 2.19],
    ["P-A8 (8vo PISO)", "THW No.4", 285.40, 41740, 12, 28.80, 4.73, 2.27],
    ["P-A9 (9no PISO)", "THW No.4", 295.40, 41740, 12, 28.80, 4.89, 2.35],
    ["P-B2 (2do PISO)", "THW No.6", 225.37, 26240, 12, 28.80, 5.94, 2.85],
    ["P-<PERSON>3 (3er P<PERSON>O)", "THW No.4", 235.37, 41740, 12, 28.80, 5.63, 2.71],
    ["P-B4 (4to PISO)", "THW No.4", 245.37, 41740, 12, 28.80, 4.06, 1.95],
    ["P-B5 (5to PISO)", "THW No.4", 255.37, 41740, 12, 28.80, 4.23, 2.03],
    ["P-B6 (6to PISO)", "THW No.4", 265.37, 41740, 12, 28.80, 4.39, 2.11],
    ["P-B7 (7mo PISO)", "THW No.4", 275.37, 41740, 12, 28.80, 4.56, 2.19],
    ["P-B8 (8vo PISO)", "THW No.4", 285.37, 41740, 12, 28.80, 4.73, 2.27],
    ["P-B9 (9no PISO)", "THW No.4", 295.37, 41740, 12, 28.80, 4.89, 2.35],
    ["P-C3 (3er PISO)", "THW No.4", 230.85, 41740, 12, 29.62, 3.93, 1.89],
    ["P-C4 (4to PISO)", "THW No.4", 240.85, 41740, 12, 29.62, 4.10, 1.97],
    ["P-C5 (5to PISO)", "THW No.4", 250.85, 41740, 12, 29.62, 4.27, 2.05],
    ["P-C6 (6to PISO)", "THW No.4", 260.85, 41740, 12, 29.62, 4.44, 2.14],
    ["P-C7 (7mo PISO)", "THW No.4", 270.85, 41740, 12, 29.62, 4.61, 2.22],
    ["P-C8 (8vo PISO)", "THW No.4", 280.75, 41740, 12, 29.62, 4.78, 2.30],
    ["P-C9 (9no PISO)", "THW No.4", 290.85, 41740, 12, 29.62, 4.95, 2.38],
    # Table 2 - Circuits D, E
    ["P-D3 (3er PISO)", "THW No.4", 183.45, 41740, 12, 39.04, 4.12, 1.98],
    ["P-D4 (4to PISO)", "THW No.4", 193.45, 41740, 12, 39.04, 4.34, 2.09],
    ["P-D5 (5to PISO)", "THW No.4", 203.45, 41740, 12, 39.04, 4.57, 2.20],
    ["P-D6 (6to PISO)", "THW No.4", 213.45, 41740, 12, 39.04, 4.79, 2.30],
    ["P-D7 (7mo PISO)", "THW No.4", 223.45, 41740, 12, 39.04, 5.02, 2.41],
    ["P-D8 (8vo PISO)", "THW No.4", 233.45, 41740, 12, 39.04, 5.24, 2.52],
    ["P-D9 (9no PISO)", "THW No.4", 243.45, 41740, 12, 39.04, 5.46, 2.63],
    ["P-E3 (3er PISO)", "THW No.4", 181.65, 41740, 12, 39.04, 4.08, 1.96],
    ["P-E4 (4to PISO)", "THW No.4", 191.65, 41740, 12, 39.04, 4.30, 2.07],
    ["P-E5 (5to PISO)", "THW No.4", 201.65, 41740, 12, 39.04, 4.53, 2.18],
    ["P-E6 (6to PISO)", "THW No.4", 211.65, 41740, 12, 39.04, 4.75, 2.28],
    ["P-E7 (7mo PISO)", "THW No.4", 221.65, 41740, 12, 39.04, 4.98, 2.39],
    ["P-E8 (8vo PISO)", "THW No.4", 231.65, 41740, 12, 39.04, 5.20, 2.50],
    ["P-E9 (9no PISO)", "THW No.4", 241.65, 41740, 12, 39.04, 5.42, 2.61]
]

# Create workbook
wb = Workbook()
ws = wb.active
ws.title = "Caída de Voltaje"

# Headers
headers = ['TRAMO', 'CONDUCTOR', 'L (PIES)', 'D² (MCM)', 'K (CM/PIES)', 'I (Amps)', 'ΔV (VOLTIOS)', '%ΔV']

# Styles
header_font = Font(name='Calibri', size=11, bold=True, color='FFFFFF')
header_fill = PatternFill(start_color='1F4E79', end_color='1F4E79', fill_type='solid')
data_font = Font(name='Calibri', size=10)
title_font = Font(name='Calibri', size=14, bold=True, color='1F4E79')

border = Border(
    left=Side(style='thin'),
    right=Side(style='thin'),
    top=Side(style='thin'),
    bottom=Side(style='thin')
)

# Add title
ws.merge_cells('A1:H1')
ws['A1'] = 'TABLA DE CAÍDA DE VOLTAJE - TORRE CARIBLU 34'
ws['A1'].font = title_font
ws['A1'].alignment = Alignment(horizontal='center', vertical='center')

# Add formulas
ws.merge_cells('A2:H2')
ws['A2'] = 'Fórmulas: ΔV = 2KIL / d²  |  %ΔV = ΔV / V × 100'
ws['A2'].font = Font(name='Calibri', size=10, italic=True, color='7030A0')
ws['A2'].alignment = Alignment(horizontal='center', vertical='center')

# Add headers
for col, header in enumerate(headers, 1):
    cell = ws.cell(row=4, column=col, value=header)
    cell.font = header_font
    cell.fill = header_fill
    cell.border = border
    cell.alignment = Alignment(horizontal='center', vertical='center')

# Add data
for row_idx, row_data in enumerate(voltage_data, 5):
    for col_idx, value in enumerate(row_data, 1):
        cell = ws.cell(row=row_idx, column=col_idx, value=value)
        cell.font = data_font
        cell.border = border
        
        if col_idx == 1:  # TRAMO column
            cell.alignment = Alignment(horizontal='left', vertical='center')
        elif col_idx == 2:  # CONDUCTOR column
            cell.alignment = Alignment(horizontal='center', vertical='center')
        else:  # Numeric columns
            cell.alignment = Alignment(horizontal='right', vertical='center')
            cell.number_format = '#,##0.00'

# Adjust column widths
column_widths = [35, 12, 12, 12, 12, 12, 12, 12]
for col_idx, width in enumerate(column_widths, 1):
    ws.column_dimensions[get_column_letter(col_idx)].width = width

# Save file
wb.save('CAIDA_VOLTAJE_TORRE_CARIBLU_34_Formatted.xlsx')

print("✅ Excel file 'CAIDA_VOLTAJE_TORRE_CARIBLU_34_Formatted.xlsx' created successfully!")
print("\n🎨 Professional formatting applied:")
print("• Engineering blue color scheme")
print("• Formula reference included")
print("• Proper numeric formatting")
print("• Auto-sized columns")
print("• Clean borders and styling")

print(f"\n📊 Data Summary:")
print(f"• Total circuits: {len(voltage_data)}")
print(f"• Voltage drops range: 3.90V - 5.94V")
print(f"• Percentage drops range: 1.87% - 2.85%")
print(f"• Conductors used: THW No.4, THW No.6")